{"title": "Overview Foundation Level Extensions", "outline": [{"level": "H1", "text": "Revision History", "page": 3}, {"level": "H1", "text": "Table of Contents", "page": 4}, {"level": "H1", "text": "Acknowledgements", "page": 5}, {"level": "H2", "text": "2.3 Learning Objectives", "page": 7}, {"level": "H4", "text": "Introduction to the Foundation Level Extensions", "page": 4}, {"level": "H4", "text": "Introduction to Foundation Level Agile Tester Extension", "page": 4}, {"level": "H4", "text": "Learning Objectives", "page": 4}, {"level": "H4", "text": "Overview of the Foundation Level Extension –", "page": 4}, {"level": "H1", "text": "References", "page": 4}, {"level": "H4", "text": "<PERSON> (Learning Objectives Lead), <PERSON><PERSON> (Exam Lead), <PERSON><PERSON> (Business Outcomes", "page": 5}, {"level": "H4", "text": "Extensions who wants a high-level introduction to the leading principles and an overview of the individual", "page": 6}, {"level": "H4", "text": "In addition, all Foundation Level syllabus learning objectives are examinable at the same K- level in an", "page": 7}, {"level": "H4", "text": "That said, each extension level exam focuses on the learning objectives defined in that extension", "page": 8}, {"level": "H1", "text": "Overview", "page": 1}, {"level": "H1", "text": "Foundation Level Extensions", "page": 1}, {"level": "H2", "text": "Version 1.0", "page": 1}, {"level": "H1", "text": "International Software Testing Qualifications Board", "page": 1}, {"level": "H1", "text": "1.", "page": 6}, {"level": "H1", "text": "2.", "page": 7}, {"level": "H2", "text": "2.1 Intended Audience", "page": 7}]}