#!/usr/bin/env python3

import fitz
from data_structures import TextBlock
from main_hackathon_optimized import GroundTruthOptimizedClassifier

# Test the exact same process as in process_pdf_hackathon
print("Testing file05.pdf processing...")

# Open PDF
doc = fitz.open('input/file05.pdf')
total_pages = doc.page_count
print(f"Total pages: {total_pages}")

# Initialize classifier
classifier = GroundTruthOptimizedClassifier()

all_blocks = []

# Extract text blocks from all pages (same as in process_pdf_hackathon)
for page_num in range(total_pages):
    page = doc[page_num]
    blocks = page.get_text("dict")
    
    for block in blocks.get("blocks", []):
        if "lines" in block:
            for line in block["lines"]:
                for span in line["spans"]:
                    text = span["text"].strip()
                    if text:
                        # Create text block
                        text_block = TextBlock(
                            text=text,
                            font_name=span.get("font", ""),
                            font_size=span.get("size", 12),
                            font_flags=0,
                            bbox=span.get("bbox", [0, 0, 0, 0]),
                            page=page_num + 1,
                            is_bold="Bold" in span.get("font", ""),
                            is_italic="Italic" in span.get("font", "")
                        )
                        all_blocks.append(text_block)

doc.close()

print(f"Total blocks extracted: {len(all_blocks)}")

# Test document type detection
doc_type, doc_config = classifier.detect_document_type(all_blocks)
print(f"Detected document type: {doc_type}")
print(f"Document config: {doc_config}")

# Find HOPE block
hope_block_index = None
for i, block in enumerate(all_blocks):
    if block.text.strip().upper() == 'HOPE':
        hope_block_index = i
        print(f"Found HOPE block at index {i}: '{block.text}' (page: {block.page})")
        break

if hope_block_index is not None:
    print(f"Next few blocks after HOPE:")
    for j in range(hope_block_index + 1, min(hope_block_index + 8, len(all_blocks))):
        block = all_blocks[j]
        print(f"  Block {j}: '{block.text}' (page: {block.page})")

# Test heading extraction
print("\nTesting heading extraction...")
headings = classifier.extract_headings_ground_truth(all_blocks, doc_type, doc_config)
print(f"Extracted headings: {headings}")

# Test full document processing
print("\nTesting full document processing...")
result = classifier.process_document(all_blocks)
print(f"Full result: {result}")
